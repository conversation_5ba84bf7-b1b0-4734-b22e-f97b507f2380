import React from 'react';
import { useMessageGetter } from '@messageformat/react';
import Badge from '@shape-construction/arch-ui/src/Badge';
import Button from '@shape-construction/arch-ui/src/Button';
import { InformationCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import Popover from '@shape-construction/arch-ui/src/Popover';
import { Tooltip, TooltipContent, TooltipTrigger } from '@shape-construction/arch-ui/src/Tooltip/Tooltip';
import { getHeatmapColorClasses, HEATMAP_LEVELS, HEATMAP_THEME } from './heatmap-config';
import { ScoringInfo } from './ScoringInfo';

export const HeatmapLegend = () => {
  const messages = useMessageGetter('dataBook.page.heatmapDashboard');
  return (
    <div className="overflow-x-auto">
      <div className="flex flex-row justify-between px-4 min-w-max items-center">
        <Popover>
          <Popover.Trigger>
            <Tooltip delayDuration={100}>
              <TooltipTrigger>
                <Button color="secondary" size="xxs" variant="outlined" leadingIcon={InformationCircleIcon}>
                  {messages('performanceDetails.issueReportsTable.scoringInfo.scoringInfoCTA')}
                </Button>
              </TooltipTrigger>
              <TooltipContent align="start">
                {messages('performanceDetails.issueReportsTable.scoringInfo.tooltipText')}
              </TooltipContent>
            </Tooltip>
          </Popover.Trigger>
          <Popover.Content align="start" className="p-0 max-h-[340px]">
            <ScoringInfo />
          </Popover.Content>
        </Popover>
        <div className="flex">
          {HEATMAP_LEVELS.slice(1).map((level) => {
            const badgeClasses = getHeatmapColorClasses(HEATMAP_THEME, level, 'badge');
            const label = messages(`healthLevels.${level}.label`);
            const scoreRange = messages(`healthLevels.${level}.scoreRange`);
            const description = messages(`healthLevels.${level}.description`);

            return (
              <div key={level} className="p-2 last:pr-0">
                <Popover>
                  <Popover.Trigger>
                    <Badge label={messages(`healthLevels.${level}.label`)} className={badgeClasses} />
                  </Popover.Trigger>
                  <Popover.Content align="start" className="p-4 bg-surface-overlay border-neutral-subtlest">
                    <div className="flex flex-col gap-4">
                      <span className='text-base leading-6 font-medium text-neutral-bold'>
                        {label}
                      </span>
                      <span className="text-sm leading-5 font-normal text-neutral">
                        {scoreRange}
                      </span>
                      <span className="text-sm leading-5 font-normal text-neutral">
                        {description}
                      </span>
                    </div>
                  </Popover.Content>
                </Popover>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
